<?php $__env->startSection('title', 'Backlog Management'); ?>
<?php $__env->startSection('resourcesite'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
.backlog-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
.unit-autocomplete {
    position: relative;
}
.unit-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}
.unit-dropdown-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}
.unit-dropdown-item:hover {
    background: #f8f9fa;
}
.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}
@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
.skeleton-row {
    height: 50px;
    margin-bottom: 10px;
    border-radius: 5px;
}
.modal-backdrop {
    z-index: 1040;
}
.modal {
    z-index: 1050;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Backlog Management</li>
                    </ol>
                </div>
                <h4 class="page-title">Backlog Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Side - Table -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">Daftar Backlog</h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-primary" id="btn-add-backlog">
                                <i class="mdi mdi-plus"></i> Tambah Backlog
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="filter-status">
                                <option value="">Semua Status</option>
                                <option value="OPEN">Open</option>
                                <option value="CLOSED">Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control form-control-sm" id="filter-unit" placeholder="Unit Code">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="filter-start-date">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="filter-end-date">
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Unit Code</th>
                                    <th>HM Found</th>
                                    <th>Problem</th>
                                    <th>Backlog Job</th>
                                    <th>Plan HM</th>
                                    <th>Status</th>
                                    <th>Plan Pull Date</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="backlog-table-body">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center" id="pagination-container">
                        <!-- Pagination will be loaded via AJAX -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Form -->
        <div class="col-md-4">
            <div class="card" id="form-card" style="display: none;">
                <div class="card-body">
                    <h4 class="header-title" id="form-title">Tambah Backlog</h4>
                    
                    <form id="backlog-form">
                        <input type="hidden" id="backlog-id">
                        
                        <div class="mb-3">
                            <label for="unit_code" class="form-label">Unit Code <span class="text-danger">*</span></label>
                            <div class="unit-autocomplete">
                                <input type="text" class="form-control" id="unit_code" name="unit_code" placeholder="Ketik untuk mencari unit..." autocomplete="off" required>
                                <div class="unit-dropdown" id="unit-dropdown"></div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="hm_found" class="form-label">HM Found</label>
                            <input type="number" class="form-control" id="hm_found" name="hm_found" step="0.01" min="0">
                        </div>

                        <div class="mb-3">
                            <label for="problem_description" class="form-label">Problem Description <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="problem_description" name="problem_description" required>
                        </div>

                        <div class="mb-3">
                            <label for="backlog_job" class="form-label">Backlog Job <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="backlog_job" name="backlog_job" required>
                        </div>

                        <div class="mb-3">
                            <label for="plan_hm" class="form-label">Plan HM</label>
                            <input type="number" class="form-control" id="plan_hm" name="plan_hm" step="0.01" min="0">
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="OPEN">Open</option>
                                <option value="CLOSED">Closed</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="plan_pull_date" class="form-label">Plan Pull Date</label>
                            <input type="datetime-local" class="form-control" id="plan_pull_date" name="plan_pull_date">
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="btn-submit">
                                <i class="mdi mdi-content-save"></i> Simpan
                            </button>
                            <button type="button" class="btn btn-secondary" id="btn-cancel">
                                <i class="mdi mdi-close"></i> Batal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Edit Backlog -->
<div class="modal fade" id="editBacklogModal" tabindex="-1" aria-labelledby="editBacklogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBacklogModalLabel">Edit Backlog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-backlog-form">
                    <input type="hidden" id="edit-backlog-id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_unit_code" class="form-label">Unit Code <span class="text-danger">*</span></label>
                                <div class="unit-autocomplete">
                                    <input type="text" class="form-control" id="edit_unit_code" name="unit_code" placeholder="Ketik untuk mencari unit..." autocomplete="off" required>
                                    <div class="unit-dropdown" id="edit-unit-dropdown"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_hm_found" class="form-label">HM Found</label>
                                <input type="number" class="form-control" id="edit_hm_found" name="hm_found" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_problem_description" class="form-label">Problem Description <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_problem_description" name="problem_description" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_backlog_job" class="form-label">Backlog Job <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_backlog_job" name="backlog_job" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_plan_hm" class="form-label">Plan HM</label>
                                <input type="number" class="form-control" id="edit_plan_hm" name="plan_hm" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="edit_status" name="status" required>
                                    <option value="OPEN">Open</option>
                                    <option value="CLOSED">Closed</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_plan_pull_date" class="form-label">Plan Pull Date</label>
                        <input type="datetime-local" class="form-control" id="edit_plan_pull_date" name="plan_pull_date">
                    </div>

                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="mdi mdi-close"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btn-update-backlog">
                    <i class="mdi mdi-content-save"></i> Update
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let isEditing = false;
    let editingId = null;
    let currentPage = 1;

    // Load initial data
    loadBacklogData();

    // Show form
    $('#btn-add-backlog').click(function() {
        showForm();
    });

    // Hide form
    $('#btn-cancel').click(function() {
        hideForm();
    });

    // Unit autocomplete for add form
    $('#unit_code').on('input', function() {
        const search = $(this).val();
        if (search.length >= 2) {
            searchUnits(search, 'unit-dropdown');
        } else {
            hideUnitDropdown('unit-dropdown');
        }
    });

    // Unit autocomplete for edit form
    $('#edit_unit_code').on('input', function() {
        const search = $(this).val();
        if (search.length >= 2) {
            searchUnits(search, 'edit-unit-dropdown');
        } else {
            hideUnitDropdown('edit-unit-dropdown');
        }
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.unit-autocomplete').length) {
            hideUnitDropdown('unit-dropdown');
            hideUnitDropdown('edit-unit-dropdown');
        }
    });

    // Form submission
    $('#backlog-form').submit(function(e) {
        e.preventDefault();
        submitForm();
    });

    // Edit button
    $(document).on('click', '.btn-edit', function() {
        const id = $(this).data('id');
        editBacklog(id);
    });

    // Delete button
    $(document).on('click', '.btn-delete', function() {
        const id = $(this).data('id');
        deleteBacklog(id);
    });

    // Update button in modal
    $('#btn-update-backlog').click(function() {
        updateBacklog();
    });

    // Filters - use debounce for better performance
    let filterTimeout;
    $('#filter-status, #filter-unit, #filter-start-date, #filter-end-date').on('change input', function() {
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(function() {
            currentPage = 1;
            loadBacklogData();
        }, 300);
    });

    // Pagination click handler
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        if (url) {
            const page = new URL(url).searchParams.get('page');
            currentPage = page || 1;
            loadBacklogData();
        }
    });

    // Load backlog data via AJAX
    function loadBacklogData() {
        showSkeletonLoader();

        const params = {
            page: currentPage,
            status: $('#filter-status').val(),
            unit_code: $('#filter-unit').val(),
            start_date: $('#filter-start-date').val(),
            end_date: $('#filter-end-date').val()
        };

        $.ajax({
            url: '<?php echo e(route("backlogs.data")); ?>',
            type: 'GET',
            data: params,
            success: function(response) {
                renderBacklogTable(response.data);
                renderPagination(response.pagination);
            },
            error: function() {
                $('#backlog-table-body').html('<tr><td colspan="9" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>');
                $('#pagination-container').html('');
            }
        });
    }

    function showSkeletonLoader() {
        let skeletonHtml = '';
        for (let i = 0; i < 5; i++) {
            skeletonHtml += `
                <tr>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                </tr>
            `;
        }
        $('#backlog-table-body').html(skeletonHtml);
    }

    function renderBacklogTable(backlogs) {
        let html = '';

        if (backlogs.length === 0) {
            html = '<tr><td colspan="9" class="text-center text-muted">Tidak ada data backlog</td></tr>';
        } else {
            backlogs.forEach(function(backlog) {
                html += `
                    <tr>
                        <td>${backlog.formatted_created_date}</td>
                        <td>
                            <strong>${backlog.unit_code}</strong><br>
                            <small class="text-muted">${backlog.unit_type || '-'}</small>
                        </td>
                        <td>${backlog.hm_found || '-'}</td>
                        <td>${backlog.problem_description}</td>
                        <td>${backlog.backlog_job}</td>
                        <td>${backlog.plan_hm || '-'}</td>
                        <td>
                            <span class="badge ${backlog.status_badge_class}">${backlog.status}</span>
                        </td>
                        <td>${backlog.formatted_plan_pull_date || '-'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary btn-edit" data-id="${backlog.id}">
                                    <i class="mdi mdi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-delete" data-id="${backlog.id}">
                                    <i class="mdi mdi-delete"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        $('#backlog-table-body').html(html);
    }

    function renderPagination(pagination) {
        $('#pagination-container').html(pagination);
    }

    function showForm() {
        $('#form-card').show();
        $('#form-title').text('Tambah Backlog');
        $('#backlog-form')[0].reset();
        $('#backlog-id').val('');
        isEditing = false;
        editingId = null;
    }

    function hideForm() {
        $('#form-card').hide();
        $('#backlog-form')[0].reset();
        isEditing = false;
        editingId = null;
    }

    function searchUnits(search, dropdownId) {
        $.ajax({
            url: '<?php echo e(route("backlogs.search-units")); ?>',
            type: 'GET',
            data: { search: search },
            success: function(response) {
                renderUnitDropdown(response.data, dropdownId);
            },
            error: function() {
                hideUnitDropdown(dropdownId);
            }
        });
    }

    function renderUnitDropdown(units, dropdownId) {
        let html = '';

        if (units.length === 0) {
            html = '<div class="unit-dropdown-item text-muted">Unit tidak ditemukan</div>';
        } else {
            units.forEach(function(unit) {
                const targetInput = dropdownId === 'edit-unit-dropdown' ? 'edit_unit_code' : 'unit_code';
                html += `
                    <div class="unit-dropdown-item" onclick="selectUnit('${unit.unit_code}', '${unit.unit_type}', '${targetInput}', '${dropdownId}')">
                        <strong>${unit.unit_code}</strong><br>
                        <small class="text-muted">${unit.unit_type}</small>
                    </div>
                `;
            });
        }

        $('#' + dropdownId).html(html).show();
    }

    function hideUnitDropdown(dropdownId) {
        $('#' + dropdownId).hide();
    }

    window.selectUnit = function(unitCode, unitType, inputId, dropdownId) {
        $('#' + inputId).val(unitCode);
        hideUnitDropdown(dropdownId);
    };

    function submitForm() {
        const formData = new FormData($('#backlog-form')[0]);

        $.ajax({
            url: '<?php echo e(route("backlogs.store")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        hideForm();
                        loadBacklogData();
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = '';
                    Object.keys(errors).forEach(key => {
                        errorMessage += errors[key][0] + '\n';
                    });

                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Error',
                        text: errorMessage
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat menyimpan data'
                    });
                }
            }
        });
    }

    function editBacklog(id) {
        $.ajax({
            url: `/backlogs/${id}`,
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    const backlog = response.data;

                    // Fill the edit form
                    $('#edit-backlog-id').val(backlog.id);
                    $('#edit_unit_code').val(backlog.unit_code);
                    $('#edit_hm_found').val(backlog.hm_found);
                    $('#edit_problem_description').val(backlog.problem_description);
                    $('#edit_backlog_job').val(backlog.backlog_job);
                    $('#edit_plan_hm').val(backlog.plan_hm);
                    $('#edit_status').val(backlog.status);
                    $('#edit_notes').val(backlog.notes);

                    // Format datetime for input
                    if (backlog.plan_pull_date) {
                        const date = new Date(backlog.plan_pull_date);
                        const formattedDate = date.toISOString().slice(0, 16);
                        $('#edit_plan_pull_date').val(formattedDate);
                    }

                    // Show modal
                    $('#editBacklogModal').modal('show');
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Terjadi kesalahan saat mengambil data backlog'
                });
            }
        });
    }

    function updateBacklog() {
        const formData = new FormData($('#edit-backlog-form')[0]);
        const id = $('#edit-backlog-id').val();
        formData.append('_method', 'PUT');

        $.ajax({
            url: `/backlogs/${id}`,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        $('#editBacklogModal').modal('hide');
                        loadBacklogData();
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = '';
                    Object.keys(errors).forEach(key => {
                        errorMessage += errors[key][0] + '\n';
                    });

                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Error',
                        text: errorMessage
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat memperbarui data'
                    });
                }
            }
        });
    }

    function deleteBacklog(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus backlog ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/backlogs/${id}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message,
                                timer: 1500,
                                showConfirmButton: false
                            }).then(() => {
                                loadBacklogData();
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Terjadi kesalahan saat menghapus data'
                        });
                    }
                });
            }
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/backlogs/index.blade.php ENDPATH**/ ?>